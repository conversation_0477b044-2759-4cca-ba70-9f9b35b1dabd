<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح النصوص في النموذج العصري</title>
    <link rel="stylesheet" href="assets/css/rid-cod.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            color: #2e7d32;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h3>اختبار إصلاح النصوص في النموذج العصري</h3>
            <p><strong>المشكلة:</strong> النصوص (placeholders) في حقول الاسم والهاتف تظهر وتختفي بسرعة</p>
            <p><strong>الحل:</strong> تم إزالة تأثير floating labels وإصلاح CSS</p>
            <p><strong>التوقع:</strong> النصوص يجب أن تبقى ثابتة ومرئية في جميع الأوقات</p>
        </div>

        <div class="rid-cod-form-modern">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>للطلب أدخل معلوماتك في الخانات أسفله ثم إضغط على زر طلب الآن 👇</h3>
                </div>

                <form id="rid-cod-form" class="checkout">
                    <div class="rid-cod-customer-info">
                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-user"></span>
                            <input type="text" id="rid-cod-full-name" name="full_name" placeholder="الاسم الكامل" required>
                        </div>

                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-phone"></span>
                            <input type="tel" id="rid-cod-phone" name="phone" placeholder="رقم الهاتف" required>
                        </div>

                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-state"></span>
                            <select id="rid-cod-state" name="state" required>
                                <option value="" disabled selected>الولاية</option>
                                <option value="01">01 - أدرار</option>
                                <option value="02">02 - الشلف</option>
                                <option value="16">16 - الجزائر</option>
                            </select>
                        </div>

                        <div class="rid-cod-field-group rid-cod-field-with-icon">
                            <span class="rid-input-icon rid-icon-city"></span>
                            <select id="rid-cod-city" name="city" required>
                                <option value="" disabled selected>البلدية</option>
                                <option value="adrar-center">أدرار المركز</option>
                                <option value="reggane">رقان</option>
                            </select>
                        </div>
                    </div>

                    <!-- Test Variations -->
                    <div class="rid-cod-variations">
                        <div class="variations-container">
                            <div class="variation-row">
                                <div class="variation-label-text">اللون:</div>
                                <div class="variation-boxes" data-attribute="color">
                                    <div class="variation-option color-option" style="background-color: #ff0000;" title="أحمر"></div>
                                    <div class="variation-option color-option" style="background-color: #00ff00;" title="أخضر"></div>
                                    <div class="variation-option color-option selected" style="background-color: #000000;" title="أسود"></div>
                                </div>
                            </div>
                            
                            <div class="variation-row">
                                <div class="variation-label-text">الحجم:</div>
                                <div class="variation-boxes" data-attribute="size">
                                    <div class="variation-option">XS</div>
                                    <div class="variation-option">XL</div>
                                    <div class="variation-option">S</div>
                                    <div class="variation-option">M</div>
                                    <div class="variation-option selected">L</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Row -->
                    <div class="rid-cod-actions-row">
                        <div class="rid-cod-submit">
                            <button type="submit" id="rid-cod-submit-btn">اطلب الآن</button>
                        </div>
                        <div class="rid-cod-quantity">
                            <div class="rid-cod-quantity-selector">
                                <button type="button" id="rid-cod-decrease">-</button>
                                <input type="number" id="rid-cod-quantity-input" value="1" min="1" max="99" readonly>
                                <button type="button" id="rid-cod-increase">+</button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Order Summary -->
                <div id="rid-cod-summary-wrapper">
                    <div id="rid-cod-summary-header">
                        <h4><span class="rid-icon-cart"></span> ملخص الطلب</h4>
                        <span id="rid-cod-summary-toggle" class="rid-icon-arrow-down"></span>
                    </div>
                    <div id="rid-cod-summary-content">
                        <table>
                            <tr>
                                <td>
                                    <span class="product-name">منتج تجريبي</span>
                                    <div class="rid-summary-variation-details">
                                        <span class="variation-detail">اللون: أسود</span>
                                        <span class="variation-detail">الحجم: L</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="rid-summary-quantity">x<span>1</span></span>
                                    <span>123 دج</span>
                                </td>
                            </tr>
                            <tr>
                                <td>سعر الشحن</td>
                                <td>400 دج</td>
                            </tr>
                            <tr id="summary-delivery-type-row">
                                <td>نوع التوصيل</td>
                                <td>
                                    <div class="summary-delivery-options">
                                        <label class="summary-delivery-option">
                                            <input type="radio" name="delivery_type" value="desk" checked>
                                            <span class="summary-radio-custom"></span>
                                            <span class="summary-delivery-text">توصيل للمكتب</span>
                                        </label>
                                        <label class="summary-delivery-option">
                                            <input type="radio" name="delivery_type" value="home">
                                            <span class="summary-radio-custom"></span>
                                            <span class="summary-delivery-text">توصيل للمنزل</span>
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr class="rid-cod-total">
                                <td>السعر الإجمالي</td>
                                <td>523 دج</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            console.log('تم تحميل اختبار إصلاح النصوص');
            
            // Test that placeholders are visible
            setTimeout(function() {
                var nameInput = $('#rid-cod-full-name');
                var phoneInput = $('#rid-cod-phone');
                
                console.log('Name placeholder:', nameInput.attr('placeholder'));
                console.log('Phone placeholder:', phoneInput.attr('placeholder'));
                
                // Check if floating labels exist (they shouldn't)
                var floatingLabels = $('.floating-label');
                if (floatingLabels.length > 0) {
                    console.warn('تحذير: تم العثور على floating labels:', floatingLabels.length);
                } else {
                    console.log('✅ لا توجد floating labels - الإصلاح نجح');
                }
            }, 1000);
            
            // Test focus events
            $('input').on('focus', function() {
                console.log('Focus on:', $(this).attr('placeholder'));
            });
            
            $('input').on('blur', function() {
                console.log('Blur from:', $(this).attr('placeholder'));
            });
        });
    </script>
</body>
</html>
