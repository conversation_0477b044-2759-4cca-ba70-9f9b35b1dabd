<?php
/**
 * Test file for RID COD Order Limiter functionality
 * This file can be used to test the new order limiting features
 * 
 * Usage: Include this file in WordPress admin or run via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test the new order limiter functionality
 */
function test_rid_cod_order_limiter() {
    echo "<h2>اختبار ميزة تحديد الطلبات الجديدة</h2>";
    
    // Test 1: Check if settings are properly saved
    echo "<h3>اختبار 1: فحص الإعدادات</h3>";
    $prevent_orders = get_option('rid_cod_prevent_2nd_order_24h', 'no');
    $order_count = get_option('rid_cod_order_limit_count', 1);
    $order_hours = get_option('rid_cod_order_limit_hours', 24);
    
    echo "<p>تفعيل تحديد الطلبات: " . ($prevent_orders === 'yes' ? 'مفعل' : 'معطل') . "</p>";
    echo "<p>عدد الطلبات المسموحة: " . $order_count . "</p>";
    echo "<p>الفترة الزمنية (ساعات): " . $order_hours . "</p>";
    
    // Test 2: Check if Order Limiter class exists
    echo "<h3>اختبار 2: فحص فئة Order Limiter</h3>";
    if (class_exists('RID_COD_Order_Limiter')) {
        echo "<p style='color: green;'>✓ فئة RID_COD_Order_Limiter موجودة</p>";
        
        // Test 3: Test order counting functionality
        echo "<h3>اختبار 3: اختبار عد الطلبات</h3>";
        
        // Create a test instance
        $limiter = new RID_COD_Order_Limiter();
        
        // Test phone number counting (using reflection to access private method)
        $reflection = new ReflectionClass($limiter);
        $method = $reflection->getMethod('get_recent_phone_order_count');
        $method->setAccessible(true);
        
        $test_phone = '0123456789';
        $count = $method->invoke($limiter, $test_phone, 24);
        echo "<p>عدد الطلبات للرقم " . $test_phone . " خلال 24 ساعة: " . $count . "</p>";
        
        // Test IP counting
        $ip_method = $reflection->getMethod('get_recent_ip_order_count');
        $ip_method->setAccessible(true);
        
        $test_ip = '***********';
        $ip_count = $ip_method->invoke($limiter, $test_ip, 24);
        echo "<p>عدد الطلبات للـ IP " . $test_ip . " خلال 24 ساعة: " . $ip_count . "</p>";
        
    } else {
        echo "<p style='color: red;'>✗ فئة RID_COD_Order_Limiter غير موجودة</p>";
    }
    
    // Test 4: Test sanitization functions
    echo "<h3>اختبار 4: اختبار دوال التحقق من صحة البيانات</h3>";
    if (class_exists('RID_COD_Customizer')) {
        $customizer = new RID_COD_Customizer();
        
        // Test positive integer sanitization
        $test_values = [1, 5, 0, -1, 'abc', '10', ''];
        echo "<p>اختبار دالة sanitize_positive_integer:</p>";
        echo "<ul>";
        foreach ($test_values as $value) {
            $sanitized = $customizer->sanitize_positive_integer($value);
            echo "<li>القيمة: " . var_export($value, true) . " → النتيجة: " . $sanitized . "</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>✗ فئة RID_COD_Customizer غير موجودة</p>";
    }
    
    echo "<h3>انتهى الاختبار</h3>";
    echo "<p><strong>ملاحظة:</strong> هذا الاختبار للتطوير فقط. يرجى حذف هذا الملف من الإنتاج.</p>";
}

// Run test if accessed directly in admin
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        if (isset($_GET['test_rid_cod_limiter'])) {
            echo '<div class="notice notice-info"><div style="padding: 10px;">';
            test_rid_cod_order_limiter();
            echo '</div></div>';
        }
    });
}
