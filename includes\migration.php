<?php
/**
 * Migration script for upgrading from single-country to multi-country support
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Run migration from old settings format to new multi-country format
 */
function rid_cod_run_migration() {
    // Check if migration has already been completed
    if (get_option('rid_cod_migration_completed', false)) {
        return true;
    }
    
    error_log('RID COD: Starting migration to multi-country support...');
    
    // Step 1: Migrate old Algerian phone validation setting
    $old_phone_validation = get_option('rid_cod_enable_algerian_phone_validation', 'yes');
    update_option('rid_cod_enable_phone_validation', $old_phone_validation);
    
    // Step 2: Ensure default country is set
    $current_country = get_option('rid_cod_selected_country', '');
    if (empty($current_country)) {
        update_option('rid_cod_selected_country', 'DZ'); // Default to Algeria
        error_log('RID COD: Set default country to Algeria (DZ)');
    }
    
    // Step 3: Migrate shipping costs
    if (class_exists('RID_COD_Shipping_Manager')) {
        RID_COD_Shipping_Manager::migrate_algeria_shipping_costs();
        error_log('RID COD: Migrated Algeria shipping costs');
    }
    
    // Step 4: Clear caches
    delete_transient('rid_cod_default_costs_cache');
    delete_transient('rid_cod_wc_shipping_methods_cache');
    
    // Mark migration as completed
    update_option('rid_cod_migration_completed', true);
    update_option('rid_cod_migration_date', current_time('mysql'));
    
    error_log('RID COD: Migration completed successfully');
    
    return true;
}

/**
 * Hook migration to run when plugin is loaded
 */
add_action('plugins_loaded', 'rid_cod_run_migration', 5);

/**
 * Add migration notice for admin users - DISABLED
 */
function rid_cod_migration_admin_notice() {
    // Migration notice disabled - no longer showing success message
    return;
}
add_action('admin_notices', 'rid_cod_migration_admin_notice');

/**
 * Reset migration (for testing purposes)
 */
function rid_cod_reset_migration() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    delete_option('rid_cod_migration_completed');
    delete_option('rid_cod_migration_date');
    
    wp_redirect(admin_url('admin.php?page=rid_cod_settings&migration_reset=1'));
    exit;
}

// Add reset migration action (hidden, for debugging)
if (isset($_GET['rid_cod_reset_migration']) && current_user_can('manage_options')) {
    add_action('admin_init', 'rid_cod_reset_migration');
}
