<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>نموذج الطلب</title>
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Cairo', Arial, sans-serif;
      background: #fff;
      margin: 30px;
    }
    .order-container {
      max-width: 500px;
      margin: 0 auto;
      border: 2px solid #bfa9f2;
      border-radius: 10px;
      padding: 24px 0 0 0;
      background: #fff;
      box-sizing: border-box;
    }
    .order-header {
      text-align: center;
      font-size: 20px;
      font-weight: normal;
      margin-bottom: 20px;
      color: #222;
    }
    .order-form {
      padding: 0 24px;
    }
    .fields-row {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
    }
    .field-group {
      flex: 1;
      position: relative;
    }
    .field-input, .field-select {
      width: 100%;
      padding: 12px;
      border: 2px solid #eee;
      border-radius: 7px;
      font-size: 16px;
      background: #faf9fc;
      outline: none;
      box-sizing: border-box;
      transition: border 0.2s;
      text-align: right;
    }
    .field-input:focus, .field-select:focus {
      border-color: #bfa9f2;
    }
    .field-select {
      appearance: none;
      -webkit-appearance: none;
      background: #faf9fc url("data:image/svg+xml,%3Csvg width='11' height='6' viewBox='0 0 11 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L5.5 5L10 1' stroke='%23BFA9F2' stroke-width='2'/%3E%3C/svg%3E") no-repeat left 12px center/14px 8px;
      cursor: pointer;
      padding-left: 32px;
    }
    .color-row, .size-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      gap: 8px;
    }
    .color-label {
      min-width: 38px;
      font-size: 16px;
      margin-left: 6px;
    }
    .color-box {
      width: 28px;
      height: 28px;
      border-radius: 4px;
      border: 2px solid #eee;
      display: inline-block;
      margin-left: 4px;
      cursor: pointer;
      transition: border 0.2s;
      vertical-align: middle;
    }
    .color-box.selected, .color-box:focus {
      border: 2px solid #bfa9f2;
      box-shadow: 0 0 0 2px #e7deff;
    }
    .color-red { background: #ff2222; }
    .color-white { background: #fff; border: 2px solid #bbb; }
    .color-black { background: #222; }
    .color-green { background: #218c2a; }
    .size-label {
      min-width: 38px;
      font-size: 16px;
      margin-left: 6px;
    }
    .size-btn {
      border: 2px solid #ddd;
      background: #fff;
      color: #222;
      font-size: 15px;
      border-radius: 5px;
      padding: 3px 13px;
      margin-left: 3px;
      cursor: pointer;
      transition: border 0.2s, background 0.2s;
      font-family: 'Cairo', Arial, sans-serif;
    }
    .size-btn.selected, .size-btn:focus {
      border: 2px solid #bfa9f2;
      background: #f4f0fd;
      font-weight: bold;
    }
    .order-btn-row {
      margin: 28px 0 0 0;
      display: flex;
      justify-content: center;
    }
    .order-btn {
      width: 100%;
      background: #bfa9f2;
      color: #fff;
      font-size: 18px;
      border: none;
      border-radius: 12px;
      padding: 12px 0;
      font-family: 'Cairo', Arial, sans-serif;
      box-shadow: 0 4px 16px #bfa9f260;
      cursor: pointer;
      transition: background 0.2s;
    }
    .order-btn:active, .order-btn:focus {
      background: #a88edc;
    }
    .qty-row {
      margin: 18px 0 0 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
    }
    .qty-btn {
      width: 36px;
      height: 36px;
      border: 1.5px solid #bfa9f2;
      background: #fff;
      border-radius: 6px;
      color: #222;
      font-size: 22px;
      cursor: pointer;
      transition: background 0.15s, border 0.15s;
      font-family: 'Cairo', Arial, sans-serif;
    }
    .qty-btn:active {
      background: #eee7ff;
    }
    .qty-input {
      width: 40px;
      text-align: center;
      font-size: 18px;
      border: none;
      background: transparent;
      font-family: 'Cairo', Arial, sans-serif;
    }
    /* Summary Section */
    .summary-section {
      margin-top: 32px;
      border-top: 2px solid #bfa9f2;
      background: #f6f1fa;
      border-radius: 0 0 10px 10px;
      overflow: hidden;
      font-size: 17px;
    }
    .summary-header {
      display: flex;
      align-items: center;
      padding: 10px 24px 10px 10px;
      background: #e6d9fc;
      color: #a88edc;
      font-weight: bold;
      font-size: 17px;
      border-bottom: 1.5px solid #bfa9f2;
      cursor: pointer;
      justify-content: space-between;
    }
    .summary-header .cart-icon {
      margin-right: 6px;
      font-size: 20px;
      color: #bfa9f2;
      vertical-align: middle;
    }
    .summary-toggle {
      font-size: 22px;
      color: #bfa9f2;
      margin-left: 10px;
      vertical-align: middle;
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
    .summary-body {
      padding: 10px 24px 0 24px;
    }
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 12px 0 0 0;
      font-size: 16px;
    }
    .summary-item .item-title {
      font-weight: bold;
      color: #222;
    }
    .summary-item .item-value {
      color: #222;
      font-weight: normal;
      display: flex;
      align-items: center;
    }
    .summary-item .badge {
      background: #bfa9f2;
      color: #fff;
      font-size: 12px;
      border-radius: 7px;
      padding: 0 6px;
      margin-right: 4px;
      vertical-align: middle;
      font-weight: bold;
      margin-top: -2px;
    }
    .shipping-section {
      margin: 18px 0 0 0;
      border-top: 1px solid #e0cffe;
      padding-top: 10px;
      color: #444;
    }
    .shipping-label {
      font-size: 15px;
      font-weight: bold;
      margin-bottom: 4px;
      color: #222;
    }
    .shipping-option {
      display: flex;
      align-items: center;
      font-size: 16px;
      margin-top: 5px;
    }
    .shipping-option input[type="radio"] {
      accent-color: #bfa9f2;
      margin-left: 7px;
      margin-right: 3px;
      width: 17px;
      height: 17px;
    }
    .shipping-price {
      margin-left: 10px;
      font-weight: bold;
      color: #222;
    }
    .total-section {
      margin-top: 18px;
      border-top: 2px solid #e0cffe;
      padding-top: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #222;
      margin-bottom: 12px;
    }
    /* Responsive */
    @media (max-width: 600px) {
      .order-container {
        padding: 0;
        max-width: 100vw;
        border-width: 1.5px;
      }
      .order-form, .summary-header, .summary-body {
        padding-left: 10px !important;
        padding-right: 10px !important;
      }
      .fields-row {
        flex-direction: column;
        gap: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="order-container">
    <div class="order-header">
      للطلب أدخل معلوماتك في الخانات أسفله ثم إضغط على زر الطلب الان
    </div>
    <form class="order-form" autocomplete="off">
      <div class="fields-row">
        <div class="field-group">
          <input class="field-input" type="text" placeholder="الاسم الكامل">
        </div>
        <div class="field-group">
          <input class="field-input" type="tel" placeholder="رقم الهاتف">
        </div>
      </div>
      <div class="fields-row">
        <div class="field-group">
          <select class="field-select">
            <option>المدينة</option>
            <option>الجزائر</option>
            <option>أدرار - Adrar 01</option>
            <option>سطيف</option>
          </select>
        </div>
        <div class="field-group">
          <select class="field-select">
            <option>Adrar 01 - أدرار</option>
            <option>الجزائر</option>
            <option>سطيف</option>
          </select>
        </div>
      </div>
      <div class="color-row" style="margin-top:15px;">
        <span class="color-label">: اللون</span>
        <span tabindex="0" class="color-box color-white" title="أبيض"></span>
        <span tabindex="0" class="color-box color-red" title="أحمر"></span>
        <span tabindex="0" class="color-box color-black selected" title="أسود"></span>
        <span tabindex="0" class="color-box color-green" title="أخضر"></span>
      </div>
      <div class="size-row">
        <span class="size-label">: الحجم</span>
        <button type="button" class="size-btn">XS</button>
        <button type="button" class="size-btn">XL</button>
        <button type="button" class="size-btn">S</button>
        <button type="button" class="size-btn selected">M</button>
        <button type="button" class="size-btn">L</button>
      </div>
      <div class="order-btn-row">
        <button type="button" class="order-btn">اطلب الآن</button>
      </div>
      <div class="qty-row">
        <button type="button" class="qty-btn" tabindex="0">+</button>
        <input class="qty-input" type="text" value="1" readonly>
        <button type="button" class="qty-btn" tabindex="0">-</button>
      </div>
    </form>
    <div class="summary-section">
      <div class="summary-header">
        <span>
          <span class="summary-toggle">&#x25B2;</span>
          ملخص الطلب
        </span>
        <span class="cart-icon">&#128722;</span>
      </div>
      <div class="summary-body">
        <div class="summary-item">
          <span class="item-value">
            <span class="badge">x1</span>
            123 دج
          </span>
          <span class="item-title">مطحنة التوابل الكورية</span>
        </div>
        <div class="shipping-section">
          <div class="shipping-label">سعر الشحن</div>
          <div class="shipping-option">
            <input type="radio" name="shipping" checked>
            <span>توصيل المكتب</span>
            <span class="shipping-price">400 دج</span>
          </div>
          <div class="shipping-option">
            <input type="radio" name="shipping">
            <span>توصيل المنزل</span>
            <span class="shipping-price"></span>
          </div>
        </div>
        <div class="total-section">
          <span>السعر الإجمالي</span>
          <span>523 دج</span>
        </div>
      </div>
    </div>
  </div>
</body>
</html>