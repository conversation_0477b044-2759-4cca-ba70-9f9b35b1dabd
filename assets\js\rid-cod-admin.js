jQuery(document).ready(function($) {
    // Functionality for admin settings tabs

    // The PHP side handles setting the 'active' class based on URL param during page load.
    // The CSS (.tab-content { display: none; } .tab-content.active { display: block; })
    // should be sufficient to show the correct tab initially.
    // No explicit JS show/hide needed on load.

    // Handle tab clicks (using delegation for potentially dynamic elements)
    $('.nav-tab-wrapper').on('click', '.nav-tab', function(e) {
        // Prevent default link behavior if needed, but WP tabs usually rely on page reload with query param
        // e.preventDefault();

        // // Get the target tab content ID from the href (e.g., #tab-labels)
        // var targetTab = $(this).attr('href').split('tab=')[1];
        // var targetContent = '#tab-' + targetTab;

        // // Remove active class from all tabs and content
        // $('.nav-tab').removeClass('nav-tab-active');
        // $('.tab-content').hide();

        // // Add active class to the clicked tab and show its content
        // $(this).addClass('nav-tab-active');
        // $(targetContent).show();

        // // Optional: Update URL hash without reloading (if not using query params)
        // // window.location.hash = targetTab;
    });

    // Note: The current PHP implementation reloads the page with a '?tab=' parameter.
    // The JavaScript above mainly ensures the correct tab is shown on page load.
    // The click handling part is commented out because the page reload handles the active state.
    // --- Copy Apps Script Code Button ---
    console.log('RID COD Admin JS: Script loaded.'); // Debug log
    var copyButton = $('#rid-cod-copy-script-button');

    if (copyButton.length) {
        console.log('RID COD Admin JS: Copy button found.'); // Debug log
        copyButton.on('click', function() {
            console.log('RID COD Admin JS: Copy button clicked.'); // Debug log
            var codeBlock = document.getElementById('rid-cod-apps-script-code');
            if (!codeBlock) {
                console.error('RID COD Admin JS: Code block element not found!'); // Debug log
                alert('خطأ: لم يتم العثور على عنصر الكود.'); // Error: Code element not found.
                return;
            }
            var textToCopy = codeBlock.innerText || codeBlock.textContent; // Get text content

            // Use the modern Clipboard API if available (requires HTTPS or localhost)
            if (navigator.clipboard && window.isSecureContext) {
                console.log('RID COD Admin JS: Using Clipboard API.'); // Debug log
                navigator.clipboard.writeText(textToCopy).then(function() {
                    // Success feedback (optional)
                    alert('تم نسخ الكود!'); // Alert: Code copied!
                }, function(err) {
                    // Error feedback (optional)
                    console.error('RID COD: Failed to copy code using Clipboard API: ', err);
                    fallbackCopyTextToClipboard(textToCopy); // Try fallback
                });
            } else {
                // Fallback for older browsers or insecure contexts
                console.log('RID COD Admin JS: Using fallback copy method.'); // Debug log
                fallbackCopyTextToClipboard(textToCopy);
            }
        }); // End of click handler
    } else {
         console.log('RID COD Admin JS: Copy button not found.'); // Debug log
    }

    // Fallback function for copying text
    function fallbackCopyTextToClipboard(text) {
        var textArea = document.createElement("textarea");
        textArea.value = text;

        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            var successful = document.execCommand('copy');
            if (successful) {
                alert('تم نسخ الكود!'); // Alert: Code copied!
            } else {
                 alert('فشل نسخ الكود.'); // Alert: Failed to copy code.
                 console.error('RID COD: Fallback copy command failed');
            }
        } catch (err) {
             alert('فشل نسخ الكود.'); // Alert: Failed to copy code.
             console.error('RID COD: Error during fallback copy command: ', err);
        }

        document.body.removeChild(textArea);
    }
    // --- End Copy Button ---

    // --- Copy Shortcode Button ---
    var copyShortcodeButton = $('#rid-cod-copy-shortcode-button');
    if (copyShortcodeButton.length) {
        console.log('RID COD Admin JS: Shortcode copy button found.'); // Added log
        copyShortcodeButton.on('click', function() {
            console.log('RID COD Admin JS: Shortcode copy button clicked.'); // Added log
            var shortcodeInput = document.getElementById('rid-cod-shortcode-text');
            if (!shortcodeInput) {
                alert('خطأ: لم يتم العثور على حقل الكود المختصر.'); // Error: Shortcode input field not found.
                return;
            }
            var textToCopy = shortcodeInput.value;

            // Use the same copy logic (modern API or fallback)
            if (navigator.clipboard && window.isSecureContext) {
                console.log('RID COD Admin JS: Attempting shortcode copy via Clipboard API.'); // Added log
                navigator.clipboard.writeText(textToCopy).then(function() {
                    console.log('RID COD Admin JS: Shortcode copied via Clipboard API successfully.'); // Added log
                    alert('تم نسخ الكود المختصر!'); // Alert: Shortcode copied!
                }, function(err) {
                    console.error('RID COD: Failed to copy shortcode using Clipboard API: ', err);
                    fallbackCopyTextToClipboard(textToCopy); // Try fallback
                });
            } else {
                console.log('RID COD Admin JS: Attempting shortcode copy via fallback method.'); // Added log
                fallbackCopyTextToClipboard(textToCopy);
            }
        });
    }
    // --- End Copy Shortcode Button ---

    // Initialize WordPress Color Picker
    $('.rid-cod-color-picker').wpColorPicker();
 
    // --- Clear Shipping Cache Button ---
    var $clearCacheButton = $('#rid-cod-clear-shipping-cache-btn');
    var $cacheStatus = $('#rid-cod-cache-status');
    var $spinner = $clearCacheButton.siblings('.spinner');
 
    if ($clearCacheButton.length) {
        $clearCacheButton.on('click', function() {
            // Show spinner, disable button, clear status
            $spinner.addClass('is-active');
            $clearCacheButton.prop('disabled', true);
            $cacheStatus.hide().removeClass('notice-success notice-error').text('');
 
            // AJAX request
            $.ajax({
                url: ridCodAdminData.ajaxUrl, // Localized URL
                type: 'POST',
                data: {
                    action: 'rid_cod_clear_shipping_cache', // PHP action hook
                    nonce: ridCodAdminData.clearCacheNonce // Localized nonce
                },
                success: function(response) {
                    if (response.success) {
                        $cacheStatus.text(response.data).addClass('notice-success').show();
                    } else {
                        // Display error message from server if available, otherwise generic
                        var errorMessage = response.data ? ridCodAdminData.errorText + ' ' + response.data : ridCodAdminData.errorText + ' Unknown error.';
                        $cacheStatus.text(errorMessage).addClass('notice-error').show();
                        console.error('RID COD Cache Clear Error:', response);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $cacheStatus.text(ridCodAdminData.errorText + ' ' + textStatus).addClass('notice-error').show();
                    console.error('RID COD Cache Clear AJAX Error:', textStatus, errorThrown);
                },
                complete: function() {
                    // Hide spinner, re-enable button
                    $spinner.removeClass('is-active');
                    $clearCacheButton.prop('disabled', false);
                }
            });
        });
    }
    // --- End Clear Shipping Cache Button ---
 
}); // End of jQuery(document).ready()