# ميزة تحديد الطلبات المطورة - RID COD Plugin

## نظرة عامة
تم تطوير ميزة تحديد الطلبات لتوفير مرونة أكبر للمستخدمين في التحكم بعدد الطلبات المسموحة للعملاء.

## الميزات الجديدة

### 1. إعدادات قابلة للتخصيص
بدلاً من القيد الثابت (طلب واحد كل 24 ساعة)، يمكن الآن تخصيص:
- **عدد الطلبات المسموحة**: يمكن تحديد أي عدد من الطلبات (1، 2، 3، إلخ)
- **الفترة الزمنية**: يمكن تحديد أي فترة زمنية بالساعات (1، 6، 12، 24، 48، إلخ)

### 2. واجهة محسنة
- حقول منفصلة لعدد الطلبات والفترة الزمنية
- ملاحظات توضيحية ومثال تفاعلي
- تفعيل/تعطيل الحقول بناءً على الإعداد الرئيسي

### 3. منطق محسن
- دعم طلبات متعددة بدلاً من طلب واحد فقط
- تنظيف تلقائي للبيانات المنتهية الصلاحية
- رسائل خطأ ديناميكية تعكس الإعدادات الحالية

## كيفية الاستخدام

### في لوحة التحكم
1. اذهب إلى **WooCommerce > إعدادات RID COD**
2. انتقل إلى تبويبة **"إعدادات مكافحة الطلبات الوهمية"**
3. فعّل **"تفعيل تحديد الطلبات"**
4. حدد **عدد الطلبات المسموحة** (مثال: 3)
5. حدد **الفترة الزمنية بالساعات** (مثال: 12)
6. احفظ الإعدادات

### أمثلة على الاستخدام
- **3 طلبات كل 12 ساعة**: يمكن للعميل تقديم 3 طلبات كحد أقصى خلال 12 ساعة
- **1 طلب كل 6 ساعات**: يمكن للعميل تقديم طلب واحد كل 6 ساعات
- **5 طلبات كل 48 ساعة**: يمكن للعميل تقديم 5 طلبات خلال يومين

## التغييرات التقنية

### ملفات معدلة
1. **includes/class-rid-cod-customizer.php**
   - إضافة حقول جديدة للإعدادات
   - دالة عرض مخصصة للإعدادات المجمعة
   - دالة تحقق من صحة الأرقام الموجبة
   - CSS مخصص لتحسين المظهر

2. **includes/class-rid-cod-order-limiter.php**
   - تحديث منطق فحص الطلبات لدعم العد المتعدد
   - دوال جديدة لحساب عدد الطلبات
   - تحديث آلية تسجيل الطلبات لدعم طلبات متعددة
   - تحديث دالة التنظيف لاستخدام الفترة القابلة للتخصيص

### إعدادات جديدة
- `rid_cod_order_limit_count`: عدد الطلبات المسموحة (افتراضي: 1)
- `rid_cod_order_limit_hours`: الفترة الزمنية بالساعات (افتراضي: 24)

### دوال جديدة
- `get_recent_phone_order_count()`: حساب عدد الطلبات لرقم هاتف
- `get_recent_ip_order_count()`: حساب عدد الطلبات لعنوان IP
- `sanitize_positive_integer()`: التحقق من صحة الأرقام الموجبة
- `render_order_limit_settings()`: عرض إعدادات تحديد الطلبات

## التوافق مع الإصدارات السابقة
- الميزة متوافقة تماماً مع الإعدادات السابقة
- الإعدادات الافتراضية تحافظ على السلوك السابق (طلب واحد كل 24 ساعة)
- لا حاجة لإعادة تكوين الإعدادات الموجودة

## الاختبار
تم إنشاء ملف اختبار `test-order-limiter.php` للتحقق من:
- حفظ الإعدادات بشكل صحيح
- عمل دوال العد والتحقق
- صحة دوال التحقق من البيانات

لتشغيل الاختبار: أضف `?test_rid_cod_limiter=1` إلى رابط أي صفحة في لوحة التحكم.

## ملاحظات مهمة
- يتم حفظ الطلبات بمفاتيح فريدة لدعم طلبات متعددة
- التنظيف التلقائي يعمل بناءً على الفترة الزمنية المحددة
- رسائل الخطأ تعكس الإعدادات الحالية للمستخدم
- الميزة تعمل على مستوى رقم الهاتف وعنوان IP منفصلين

## الدعم والصيانة
- الكود موثق بالكامل باللغة العربية والإنجليزية
- تم اتباع معايير WordPress للتطوير
- الميزة قابلة للتوسع والتطوير المستقبلي
