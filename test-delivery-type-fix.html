<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح نوع التوصيل</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .summary-delivery-options {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .summary-delivery-option {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            gap: 8px;
        }
        .summary-delivery-option input[type="radio"] {
            display: none;
        }
        .summary-radio-custom {
            width: 16px;
            height: 16px;
            border: 2px solid #b19cd9;
            border-radius: 50%;
            background: white;
            transition: all 0.2s ease;
        }
        .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom {
            background: #b19cd9;
        }
        .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
        }
        .summary-radio-custom {
            position: relative;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .price-display {
            font-size: 18px;
            font-weight: bold;
            color: #b19cd9;
            margin: 10px 0;
        }
        select, input {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>اختبار إصلاح نوع التوصيل في الملخص</h2>
        
        <div>
            <label>اختر الولاية:</label>
            <select id="test-state">
                <option value="">اختر الولاية</option>
                <option value="01" data-state="أدرار">01 - أدرار</option>
                <option value="02" data-state="الشلف">02 - الشلف</option>
                <option value="16" data-state="الجزائر">16 - الجزائر</option>
            </select>
        </div>

        <div style="margin: 20px 0;">
            <h3>نوع التوصيل في الملخص:</h3>
            <div class="summary-delivery-options">
                <label class="summary-delivery-option">
                    <input type="radio" name="delivery_type" value="desk" checked>
                    <span class="summary-radio-custom"></span>
                    <span class="summary-delivery-text">توصيل للمكتب</span>
                </label>
                <label class="summary-delivery-option">
                    <input type="radio" name="delivery_type" value="home">
                    <span class="summary-radio-custom"></span>
                    <span class="summary-delivery-text">توصيل للمنزل</span>
                </label>
            </div>
        </div>

        <div class="test-result">
            <div>الولاية المختارة: <span id="selected-state">لم يتم الاختيار</span></div>
            <div>نوع التوصيل: <span id="selected-delivery">توصيل للمكتب</span></div>
            <div class="price-display">سعر الشحن: <span id="shipping-price">0 دج</span></div>
            <div class="price-display">الإجمالي: <span id="total-price">123 دج</span></div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // محاكاة بيانات الشحن
            var shippingCosts = {
                '01': { desk: 400, home: 500 }, // أدرار
                '02': { desk: 300, home: 400 }, // الشلف
                '16': { desk: 200, home: 300 }  // الجزائر
            };
            
            var basePrice = 123;

            // تحديث الأسعار
            function updatePrices() {
                var selectedState = $('#test-state').val();
                var selectedDelivery = $('input[name="delivery_type"]:checked').val();
                var stateName = $('#test-state option:selected').data('state') || 'لم يتم الاختيار';
                var deliveryText = $('input[name="delivery_type"]:checked').parent().find('.summary-delivery-text').text();
                
                $('#selected-state').text(stateName);
                $('#selected-delivery').text(deliveryText);
                
                if (selectedState && shippingCosts[selectedState]) {
                    var shippingCost = shippingCosts[selectedState][selectedDelivery] || 0;
                    var total = basePrice + shippingCost;
                    
                    $('#shipping-price').text(shippingCost + ' دج');
                    $('#total-price').text(total + ' دج');
                } else {
                    $('#shipping-price').text('0 دج');
                    $('#total-price').text(basePrice + ' دج');
                }
            }

            // ربط الأحداث باستخدام event delegation
            $(document).on('change', '#test-state', updatePrices);
            $(document).on('change', 'input[name="delivery_type"]', updatePrices);
            
            // تهيئة أولية
            updatePrices();
            
            console.log('تم تحميل اختبار نوع التوصيل بنجاح');
        });
    </script>
</body>
</html>
