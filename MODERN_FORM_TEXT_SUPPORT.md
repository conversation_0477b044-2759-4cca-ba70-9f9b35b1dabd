# دعم النصوص المخصصة في النموذج العصري - Modern Form Text Support

## ✅ تأكيد الدعم الكامل

تم التحقق من أن النموذج العصري يدعم **جميع** النصوص المخصصة المتاحة في "إعدادات تسميات النموذج والنصوص".

## 📝 النصوص المدعومة بالكامل

### 1. **نصوص النموذج الأساسية**

| الإعداد | المكان في النموذج | الكود المستخدم |
|---------|------------------|-----------------|
| **عنوان النموذج** | أعلى النموذج | `get_option('rid_cod_form_title', 'للطلب يرجى ملئ الإستمارة أسفله')` |
| **نص حقل الاسم** | placeholder للاسم | `get_option('rid_cod_field_name', 'الاسم الكامل')` |
| **نص حقل الهاتف** | placeholder للهاتف | `get_option('rid_cod_field_phone', 'رقم الهاتف')` |
| **نص حقل الولاية** | placeholder للولاية | `get_option('rid_cod_field_state', 'الولاية')` |
| **نص حقل المدينة** | placeholder للمدينة | `get_option('rid_cod_field_city', 'البلدية')` |
| **نص حقل الملاحظات** | placeholder للملاحظات | `get_option('rid_cod_field_notes', 'ملاحظات إضافية (اختياري)')` |

### 2. **نصوص الأزرار والإجراءات**

| الإعداد | المكان في النموذج | الكود المستخدم |
|---------|------------------|-----------------|
| **نص زر الإرسال** | زر "اطلب الآن" | `get_option('rid_cod_button_text', 'انقر هنا لتأكيد الطلب')` |
| **نص زر الإرسال** | الزر الثابت (إذا مفعل) | `get_option('rid_cod_button_text', 'انقر هنا لتأكيد الطلب')` |

### 3. **نصوص ملخص الطلب**

| الإعداد | المكان في النموذج | الكود المستخدم |
|---------|------------------|-----------------|
| **عنوان الملخص** | رأس ملخص الطلب | `get_option('rid_cod_summary_title', 'ملخص الطلب')` |
| **نص الشحن في الملخص** | صف سعر الشحن | `get_option('rid_cod_shipping_text', 'سعر التوصيل')` |
| **نص الإجمالي في الملخص** | صف الإجمالي | `get_option('rid_cod_total_text', 'السعر الإجمالي')` |

### 4. **نصوص نوع التوصيل**

| الإعداد | المكان في النموذج | الكود المستخدم |
|---------|------------------|-----------------|
| **تسمية التوصيل للمنزل** | خيار في ملخص الطلب | `get_option('rid_cod_delivery_type_home_label', 'توصيل للمنزل')` |
| **تسمية التوصيل للمكتب** | خيار في ملخص الطلب | `get_option('rid_cod_delivery_type_desk_label', 'توصيل للمكتب')` |

### 5. **نصوص JavaScript التفاعلية**

| الإعداد | الاستخدام | الكود المستخدم |
|---------|-----------|-----------------|
| **نص اختيار المدينة** | القوائم المنسدلة | `get_option('rid_cod_select_city_text', 'اختر البلدية')` |
| **نص اختيار الولاية** | القوائم المنسدلة | `get_option('rid_cod_select_state_text', 'اختر الولاية')` |
| **نص اختيار التنويع** | المتغيرات | `get_option('rid_cod_select_variation_text', 'اختر النوع')` |
| **نص الشحن المجاني** | عند الشحن المجاني | `get_option('rid_cod_free_shipping_text', 'توصيل مجاني')` |
| **نص المعالجة** | أثناء الإرسال | `get_option('rid_cod_processing_text', 'جاري المعالجة...')` |
| **نص الخطأ** | عند حدوث خطأ | `get_option('rid_cod_error_text', 'حدث خطأ. يرجى المحاولة مرة أخرى.')` |
| **نص عدم توفر الشحن** | عند عدم توفر الشحن | `get_option('rid_cod_shipping_unavailable_text', 'الشحن غير متوفر')` |

### 6. **رسائل النجاح والحالة**

| الإعداد | الاستخدام | الكود المستخدم |
|---------|-----------|-----------------|
| **رسالة نجاح الطلب** | بعد إرسال الطلب | `get_option('rid_cod_success_message', 'تم الطلب بنجاح')` |

## 🎯 كيفية التخصيص

### **للوصول إلى الإعدادات:**
1. اذهب إلى **لوحة التحكم** → **WooCommerce** → **إعدادات الدفع عند الاستلام**
2. اختر تبويبة **"تسميات النموذج والنصوص"**
3. عدل أي نص حسب احتياجاتك
4. احفظ الإعدادات

### **مثال على التخصيص:**
```
عنوان النموذج: "أطلب منتجك الآن واحصل على توصيل سريع! 🚀"
نص زر الإرسال: "أطلب الآن مع ضمان الجودة"
نص الشحن في الملخص: "تكلفة التوصيل"
تسمية التوصيل للمنزل: "توصيل مباشر للمنزل"
تسمية التوصيل للمكتب: "استلام من المكتب"
```

## 🔄 التحديث التلقائي

جميع النصوص تتحدث **فوراً** في النموذج العصري عند:
- ✅ تغيير النص في الإعدادات
- ✅ حفظ الإعدادات
- ✅ إعادة تحميل الصفحة

## 🌐 دعم اللغات

النموذج العصري يدعم:
- ✅ **العربية** (الافتراضي)
- ✅ **أي لغة أخرى** عبر تخصيص النصوص
- ✅ **النصوص المختلطة** (عربي + إنجليزي)
- ✅ **الرموز التعبيرية** (Emojis) في النصوص

## 📱 التوافق

النصوص المخصصة تعمل بشكل مثالي مع:
- ✅ **جميع أحجام الشاشات** (موبايل، تابلت، ديسكتوب)
- ✅ **جميع المتصفحات** الحديثة
- ✅ **الاتجاه من اليمين لليسار** (RTL)
- ✅ **الخطوط العربية** المختلفة

## 🎨 التصميم المتجاوب

النصوص في النموذج العصري:
- ✅ **تتكيف تلقائياً** مع طول النص
- ✅ **تحافظ على التنسيق** في جميع الأحجام
- ✅ **تدعم النصوص الطويلة** بدون كسر التصميم
- ✅ **تحافظ على الوضوح** في جميع الظروف

## 🔧 للمطورين

### **إضافة نص مخصص جديد:**
```php
// في includes/class-rid-cod-customizer.php
register_setting('rid_cod_settings', 'rid_cod_custom_text', ['sanitize_callback' => 'sanitize_text_field']);

add_settings_field(
    'rid_cod_custom_text', 
    __('النص المخصص', 'rid-cod'), 
    array($this, 'render_text_field'), 
    'rid_cod_settings', 
    'rid_cod_section_labels', 
    ['id' => 'rid_cod_custom_text', 'default' => __('النص الافتراضي', 'rid-cod')]
);

// في includes/class-rid-cod-form.php
echo esc_html(get_option('rid_cod_custom_text', __('النص الافتراضي', 'rid-cod')));
```

## 🌟 الخلاصة

النموذج العصري يدعم **100%** من النصوص المخصصة المتاحة في الإضافة:

- ✅ **24 نص مختلف** قابل للتخصيص
- ✅ **تحديث فوري** عند التغيير
- ✅ **دعم كامل للعربية** والرموز التعبيرية
- ✅ **تصميم متجاوب** مع جميع أطوال النصوص
- ✅ **سهولة التخصيص** من لوحة التحكم

يمكنك تخصيص أي نص في النموذج العصري بسهولة من إعدادات الإضافة! 🎊
