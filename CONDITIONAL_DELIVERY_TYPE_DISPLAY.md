# Conditional Delivery Type Display Implementation

## 🎯 Overview

This implementation provides two different form design approaches for displaying delivery type selection and order summary sections in the WordPress plugin:

### **Design 1 (Original Design)**
- Shows delivery type selection directly in the main form area
- Optionally hides the order summary section completely
- Clean, focused form layout

### **Design 2 (Alternative Design)**  
- Shows delivery type selection in the order summary section
- Always displays the order summary section
- Comprehensive order overview approach

## ⚙️ Admin Settings

Navigate to **WooCommerce > RID COD Settings > Delivery Options** to configure:

### 1. **موقع خيار نوع التوصيل (Delivery Type Location)**
- **في النموذج الرئيسي (main_form)**: Shows delivery type in main form (Design 1)
- **في ملخص الطلب (summary)**: Shows delivery type in order summary (Design 2)
- **Default**: `summary`

### 2. **إخفاء ملخص الطلب عند وضع التوصيل في النموذج الرئيسي**
- **نعم (yes)**: Hides order summary when delivery type is in main form
- **لا (no)**: Always shows order summary regardless of delivery type location
- **Default**: `no`

## 🔧 Technical Implementation

### **PHP Changes**

#### New Settings Added:
```php
// Location of delivery type (main_form or summary)
$delivery_type_location = get_option('rid_cod_delivery_type_location', 'summary');

// Whether to hide summary when delivery type is in main form
$hide_summary_when_delivery_in_main = get_option('rid_cod_hide_summary_when_delivery_in_main', 'no') === 'yes';
```

#### Conditional Rendering:
```php
// Delivery type in main form (Design 1)
<?php if ($enable_delivery_type && $delivery_type_location === 'main_form') : ?>
<div class="rid-cod-delivery-section rid-cod-field-group">
    <!-- Delivery type options -->
</div>
<?php endif; ?>

// Delivery type in summary (Design 2)
<?php if ($enable_delivery_type && $delivery_type_location === 'summary') : ?>
<tr id="rid-cod-summary-delivery-type-row">
    <!-- Delivery type options in summary -->
</tr>
<?php endif; ?>
```

#### CSS Classes Applied:
```php
$container_classes = ['rid-cod-form-container'];
if ($enable_delivery_type && $delivery_type_location === 'main_form') {
    $container_classes[] = 'delivery-in-main-form';
    if ($hide_summary_when_delivery_in_main) {
        $container_classes[] = 'hide-summary';
    }
}
```

### **CSS Styling**

#### Main Form Delivery Type (Design 1):
```css
.rid-cod-delivery-section {
    margin-bottom: 20px;
}

.rid-delivery-options {
    display: flex;
    gap: 20px;
    align-items: center;
}

.rid-delivery-options .delivery-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border: 1px solid var(--rid-cod-border-color);
    border-radius: 6px;
    background: var(--rid-cod-primary-bg-color);
    transition: all 0.3s ease;
}
```

#### Modern Form Enhanced Styling:
```css
.rid-cod-form-modern .rid-cod-delivery-section {
    background: linear-gradient(135deg, #f8f6fc 0%, #f0ebf8 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid rgba(177, 156, 217, 0.2);
}
```

#### Conditional Summary Visibility:
```css
.rid-cod-form-container.delivery-in-main-form.hide-summary #rid-cod-summary-wrapper {
    display: none;
}
```

### **JavaScript Enhancements**

#### Event Handling:
```javascript
// Works for both locations using event delegation
$(document).on('change', 'input[name="delivery_type"]', function() {
    var currentState = stateSelect.val();
    if (currentState) {
        updateShippingMethods(currentState, stateSelect.find('option:selected').data('state'));
    }
    updateSummaryShippingType();
    saveDraftOrderToServer();
});
```

#### Visual Feedback:
```javascript
function initializeDeliveryTypeLocation() {
    if (rid_cod_params.enable_delivery_type) {
        $(document).on('change', 'input[name="delivery_type"]', function() {
            var $option = $(this).closest('.delivery-option, .summary-delivery-option');
            var $allOptions = $('input[name="delivery_type"]').closest('.delivery-option, .summary-delivery-option');
            
            $allOptions.removeClass('selected');
            $option.addClass('selected');
        });
    }
}
```

## 🎨 Design Variations

### **Design 1: Main Form Focus**
- Delivery type prominently displayed in main form
- Optional summary hiding for minimal interface
- Best for simple, focused checkout experience

### **Design 2: Summary Integration**
- Delivery type integrated into order summary
- Complete order overview always visible
- Best for detailed order confirmation

## 🔄 Migration Notes

- **Existing installations**: Will default to Design 2 (summary location) to maintain current behavior
- **New installations**: Can choose either design approach
- **Settings are backward compatible**: No existing functionality is broken

## 📱 Responsive Behavior

Both designs are fully responsive and work across all device sizes:
- Mobile devices: Delivery options stack vertically when needed
- Tablet/Desktop: Delivery options display horizontally
- Touch-friendly: All interactive elements are properly sized for touch

## 🎯 Usage Recommendations

### **Use Design 1 (Main Form) When:**
- You want a clean, minimal checkout experience
- Delivery type is a primary decision factor
- You prefer to hide order details until confirmation

### **Use Design 2 (Summary) When:**
- You want customers to see complete order details
- Delivery type is secondary to product selection
- You prefer comprehensive order overview approach

## ✅ Testing

Test both designs with:
1. Different form styles (Classic vs Modern)
2. Various screen sizes (Mobile, Tablet, Desktop)
3. Different delivery type labels
4. With and without order summary hiding
5. JavaScript functionality for price updates
