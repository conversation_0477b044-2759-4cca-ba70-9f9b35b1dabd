<?php
/**
 * RID COD Order Limiter
 * 
 * Handles the "Prevent 2nd order in 24hours" functionality
 * Prevents customers from placing multiple orders within 24 hours based on phone number and IP address
 * 
 * @package RID_COD
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class RID_COD_Order_Limiter {

    /**
     * Constructor
     */
    public function __construct() {
        // Hook into the order processing before order creation
        add_action('rid_cod_before_order_creation', array($this, 'check_order_limit'), 10, 1);
        
        // Hook into successful order creation to record the order
        add_action('rid_cod_after_order_creation', array($this, 'record_order'), 10, 2);
    }

    /**
     * Check if customer can place an order based on configurable limit
     *
     * @param array $order_data The order data being processed
     * @throws Exception if order should be blocked
     */
    public function check_order_limit($order_data) {
        // Check if the feature is enabled
        $prevent_orders = get_option('rid_cod_prevent_2nd_order_24h', 'no') === 'yes';

        if (!$prevent_orders) {
            return; // Feature disabled, allow order
        }

        $phone = isset($order_data['phone']) ? sanitize_text_field($order_data['phone']) : '';
        $customer_ip = $this->get_customer_ip();

        // Clean phone number (remove spaces and normalize format)
        $phone = preg_replace('/\s+/', '', $phone);

        if (empty($phone)) {
            return; // No phone number, can't check
        }

        // Get configurable limits
        $max_orders = get_option('rid_cod_order_limit_count', 1);
        $time_hours = get_option('rid_cod_order_limit_hours', 24);

        // Check if this phone number or IP has exceeded the order limit
        $phone_order_count = $this->get_recent_phone_order_count($phone, $time_hours);
        $ip_order_count = $this->get_recent_ip_order_count($customer_ip, $time_hours);

        if ($phone_order_count >= $max_orders || $ip_order_count >= $max_orders) {
            $error_message = sprintf(
                __('لا يمكنك تقديم أكثر من %d طلب خلال %d ساعة. يرجى المحاولة لاحقاً.', 'rid-cod'),
                $max_orders,
                $time_hours
            );

            // Log the blocked attempt for debugging
            error_log(sprintf(
                'RID COD: Blocked order limit exceeded - Phone: %s, IP: %s, Phone orders: %d, IP orders: %d, Max allowed: %d, Time window: %d hours',
                $phone,
                $customer_ip,
                $phone_order_count,
                $ip_order_count,
                $max_orders,
                $time_hours
            ));

            throw new Exception($error_message);
        }
    }

    /**
     * Record order information for future limit checking
     * 
     * @param int $order_id The created order ID
     * @param array $order_data The order data
     */
    public function record_order($order_id, $order_data) {
        // Check if the feature is enabled
        $prevent_24h_orders = get_option('rid_cod_prevent_2nd_order_24h', 'no') === 'yes';
        
        if (!$prevent_24h_orders) {
            return; // Feature disabled, don't record
        }

        $phone = isset($order_data['phone']) ? sanitize_text_field($order_data['phone']) : '';
        $customer_ip = $this->get_customer_ip();

        // Clean phone number
        $phone = preg_replace('/\s+/', '', $phone);

        if (!empty($phone)) {
            // Record phone number with timestamp
            $this->record_phone_order($phone, $order_id);
        }

        if (!empty($customer_ip)) {
            // Record IP address with timestamp
            $this->record_ip_order($customer_ip, $order_id);
        }

        // Log successful recording
        error_log(sprintf(
            'RID COD: Recorded order for limit checking - Order ID: %d, Phone: %s, IP: %s',
            $order_id,
            $phone,
            $customer_ip
        ));
    }

    /**
     * Check if there's a recent order by phone number within 24 hours
     *
     * @param string $phone The phone number to check
     * @return bool True if recent order exists
     */
    private function has_recent_order_by_phone($phone) {
        return $this->get_recent_phone_order_count($phone, 24) > 0;
    }

    /**
     * Get count of recent orders by phone number within specified hours
     *
     * @param string $phone The phone number to check
     * @param int $hours Number of hours to look back
     * @return int Number of orders found
     */
    private function get_recent_phone_order_count($phone, $hours) {
        if (empty($phone)) {
            return 0;
        }

        // Get stored phone orders
        $phone_orders = get_option('rid_cod_phone_orders', array());

        // Clean expired entries and count recent orders
        $current_time = time();
        $time_limit = $hours * 60 * 60; // Convert hours to seconds
        $order_count = 0;

        foreach ($phone_orders as $unique_key => $order_data) {
            // Remove expired entries (older than specified hours)
            if (($current_time - $order_data['timestamp']) > $time_limit) {
                unset($phone_orders[$unique_key]);
                continue;
            }

            // Count orders for this phone number
            $stored_phone = isset($order_data['phone']) ? $order_data['phone'] : $unique_key;
            if ($stored_phone === $phone) {
                $order_count++;
            }
        }

        // Update the cleaned phone orders list
        update_option('rid_cod_phone_orders', $phone_orders);

        return $order_count;
    }

    /**
     * Check if there's a recent order by IP address within 24 hours
     *
     * @param string $ip The IP address to check
     * @return bool True if recent order exists
     */
    private function has_recent_order_by_ip($ip) {
        return $this->get_recent_ip_order_count($ip, 24) > 0;
    }

    /**
     * Get count of recent orders by IP address within specified hours
     *
     * @param string $ip The IP address to check
     * @param int $hours Number of hours to look back
     * @return int Number of orders found
     */
    private function get_recent_ip_order_count($ip, $hours) {
        if (empty($ip)) {
            return 0;
        }

        // Get stored IP orders
        $ip_orders = get_option('rid_cod_ip_orders', array());

        // Clean expired entries and count recent orders
        $current_time = time();
        $time_limit = $hours * 60 * 60; // Convert hours to seconds
        $order_count = 0;

        foreach ($ip_orders as $unique_key => $order_data) {
            // Remove expired entries (older than specified hours)
            if (($current_time - $order_data['timestamp']) > $time_limit) {
                unset($ip_orders[$unique_key]);
                continue;
            }

            // Count orders for this IP address
            $stored_ip = isset($order_data['ip']) ? $order_data['ip'] : $unique_key;
            if ($stored_ip === $ip) {
                $order_count++;
            }
        }

        // Update the cleaned IP orders list
        update_option('rid_cod_ip_orders', $ip_orders);

        return $order_count;
    }

    /**
     * Record a phone number order
     *
     * @param string $phone The phone number
     * @param int $order_id The order ID
     */
    private function record_phone_order($phone, $order_id) {
        $phone_orders = get_option('rid_cod_phone_orders', array());

        // Create a unique key for each order (phone + timestamp + order_id)
        $unique_key = $phone . '_' . time() . '_' . $order_id;

        $phone_orders[$unique_key] = array(
            'phone' => $phone,
            'order_id' => $order_id,
            'timestamp' => time()
        );

        update_option('rid_cod_phone_orders', $phone_orders);
    }

    /**
     * Record an IP address order
     *
     * @param string $ip The IP address
     * @param int $order_id The order ID
     */
    private function record_ip_order($ip, $order_id) {
        $ip_orders = get_option('rid_cod_ip_orders', array());

        // Create a unique key for each order (ip + timestamp + order_id)
        $unique_key = $ip . '_' . time() . '_' . $order_id;

        $ip_orders[$unique_key] = array(
            'ip' => $ip,
            'order_id' => $order_id,
            'timestamp' => time()
        );

        update_option('rid_cod_ip_orders', $ip_orders);
    }

    /**
     * Get customer IP address
     * 
     * @return string The customer's IP address
     */
    private function get_customer_ip() {
        // Check for various IP headers in order of preference
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );

        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs (X-Forwarded-For can contain multiple IPs)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to REMOTE_ADDR even if it's a private IP
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
    }

    /**
     * Clean up expired order records (called by cron job)
     * This method can be called periodically to clean up old records
     */
    public static function cleanup_expired_records() {
        $current_time = time();

        // Get configurable time limit (default to 24 hours if not set)
        $time_hours = get_option('rid_cod_order_limit_hours', 24);
        $time_limit = $time_hours * 60 * 60; // Convert hours to seconds

        // Clean phone orders
        $phone_orders = get_option('rid_cod_phone_orders', array());
        foreach ($phone_orders as $unique_key => $order_data) {
            if (($current_time - $order_data['timestamp']) > $time_limit) {
                unset($phone_orders[$unique_key]);
            }
        }
        update_option('rid_cod_phone_orders', $phone_orders);

        // Clean IP orders
        $ip_orders = get_option('rid_cod_ip_orders', array());
        foreach ($ip_orders as $unique_key => $order_data) {
            if (($current_time - $order_data['timestamp']) > $time_limit) {
                unset($ip_orders[$unique_key]);
            }
        }
        update_option('rid_cod_ip_orders', $ip_orders);

        error_log(sprintf('RID COD: Cleaned up expired order limit records (older than %d hours)', $time_hours));
    }
}

// Initialize the order limiter
new RID_COD_Order_Limiter();
