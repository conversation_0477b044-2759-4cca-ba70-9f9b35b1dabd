<?php
/**
 * RIDCOD Product Price Modifier
 * 
 * This class modifies how WooCommerce displays product prices when variations are selected
 * Initially shows the price range, but updates to show only the selected variation price.
 *
 * @package RIDCOD
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class RID_COD_Product_Price {

    /**
     * Constructor
     */
    public function __construct() {
        // Don't modify the initial price display - let WooCommerce show the range
        // but add scripts to update it when variations are selected
        add_action('wp_footer', array($this, 'add_price_update_script'));
    }

    /**
     * Find matching product variation
     * 
     * @param WC_Product $product
     * @param array $attributes
     * @return int|false
     */
    private function find_matching_product_variation($product, $attributes) {
        $data_store = WC_Data_Store::load('product');
        return $data_store->find_matching_product_variation($product, $attributes);
    }
    
    /**
     * Add JavaScript to update the price when a variation is selected
     */
    public function add_price_update_script() {
        // Only add this script on product pages
        if (!is_product()) {
            return;
        }
        
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Store the original price range
            var originalPrice = $('.price').html();
            $('.product').attr('data-original-price', originalPrice);
            
            // Listen for WooCommerce variation selection
            $('form.variations_form').on('show_variation', function(event, variation) {
                if (variation && variation.price_html) {
                    // Update the price display with the selected variation price
                    $('.price').html(variation.price_html);
                }
            });
            
            // Handle reset to default price
            $('form.variations_form').on('hide_variation', function() {
                // Reset to the original price range
                $('.price').html($('.product').attr('data-original-price'));
            });
            
            // Handle RIDCOD custom variation selection
            $(document).on('ridcod_variation_selected', function(event, variation) {
                if (variation && variation.display_price) {
                    // Format the price in the appropriate currency format
                    var formattedPrice = formatCurrencyWithSymbol(variation.display_price);
                    $('.price').html(formattedPrice);
                }
            });
            
            // When no variation is selected, reset to price range
            $(document).on('ridcod_variation_reset', function() {
                $('.price').html($('.product').attr('data-original-price'));
            });
            
            // Format currency with appropriate symbol and format
            function formatCurrencyWithSymbol(price) {
                // Get current country currency settings from rid_cod_params if available
                var currentCountry = 'DZ'; // Default to Algeria
                var currencySymbol = 'د.ج'; // Default to Algerian Dinar
                
                // Try to get country from rid_cod_params first, then fallback to ridcodLocalizeScript
                if (typeof rid_cod_params !== 'undefined' && rid_cod_params.current_country) {
                    currentCountry = rid_cod_params.current_country;
                    // Get currency symbol from country data if available
                    if (rid_cod_params.country_data && rid_cod_params.country_data[currentCountry]) {
                        currencySymbol = rid_cod_params.country_data[currentCountry].currency_symbol || 'د.ج';
                    }
                } else if (typeof ridcodLocalizeScript !== 'undefined' && ridcodLocalizeScript.current_country) {
                    currentCountry = ridcodLocalizeScript.current_country;
                    // Fallback to manual currency mapping
                    switch(currentCountry) {
                        case 'DZ':
                            currencySymbol = 'د.ج';
                            break;
                        case 'MA':
                            currencySymbol = 'د.م';
                            break;
                        case 'SA':
                            currencySymbol = 'ر.س';
                            break;
                        case 'TN':
                            currencySymbol = 'د.ت';
                            break;
                        case 'EG':
                            currencySymbol = 'ج.م';
                            break;
                        case 'LY':
                            currencySymbol = 'د.ل';
                            break;
                        case 'JO':
                            currencySymbol = 'د.أ';
                            break;
                        default:
                            currencySymbol = 'د.ج';
                    }
                }
                
                var formattedPrice = parseFloat(price).toFixed(2).replace('.', ',');
                
                // Add thousand separators
                formattedPrice = formattedPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                
                return currencySymbol + ' ' + formattedPrice;
            }
        });
        </script>
        <?php
    }
}

// Initialize the class
new RID_COD_Product_Price();
