<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة الألوان</title>
    <link rel="stylesheet" href="assets/css/rid-cod.css">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid var(--rid-cod-accent-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .color-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .color-item {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }
        .classic-demo {
            --rid-cod-accent-color: #6a3de8;
            --rid-cod-button-bg-color: #6a3de8;
            --rid-cod-accent-color-rgb: 106, 61, 232;
        }
        .modern-demo {
            --rid-cod-accent-color: #B19CD9;
            --rid-cod-button-bg-color: #B19CD9;
            --rid-cod-accent-color-rgb: 177, 156, 217;
        }
        .form-preview {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">🎨 اختبار نظام إدارة الألوان</h1>
        
        <!-- Test Section 1: Classic Form Colors -->
        <div class="test-section classic-demo">
            <h2 class="test-title">1. النموذج الكلاسيكي - الألوان الافتراضية</h2>
            <p><strong>اللون الافتراضي:</strong> #6a3de8 (البنفسجي الداكن)</p>
            
            <div class="color-demo">
                <div class="color-item" style="background: var(--rid-cod-accent-color);">
                    اللون المميز<br>#6a3de8
                </div>
                <div class="color-item" style="background: var(--rid-cod-button-bg-color);">
                    لون خلفية الزر<br>#6a3de8
                </div>
                <div class="color-item" style="background: rgba(var(--rid-cod-accent-color-rgb), 0.2); color: #333;">
                    اللون الشفاف<br>rgba(106, 61, 232, 0.2)
                </div>
            </div>

            <div class="form-preview">
                <div class="rid-cod-form-classic">
                    <div id="rid-cod-checkout">
                        <div class="rid-cod-title">
                            <h3>نموذج كلاسيكي - عينة</h3>
                        </div>
                        <div class="rid-cod-field-group">
                            <input type="text" placeholder="مثال على حقل إدخال" style="border-color: var(--rid-cod-accent-color);">
                        </div>
                        <button style="background: var(--rid-cod-button-bg-color); color: white; border: none; padding: 10px 20px; border-radius: 5px;">
                            زر تجريبي
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Section 2: Modern Form Colors -->
        <div class="test-section modern-demo">
            <h2 class="test-title">2. النموذج العصري - الألوان الافتراضية</h2>
            <p><strong>اللون الافتراضي:</strong> #B19CD9 (البنفسجي الفاتح)</p>
            
            <div class="color-demo">
                <div class="color-item" style="background: var(--rid-cod-accent-color);">
                    اللون المميز<br>#B19CD9
                </div>
                <div class="color-item" style="background: var(--rid-cod-button-bg-color);">
                    لون خلفية الزر<br>#B19CD9
                </div>
                <div class="color-item" style="background: rgba(var(--rid-cod-accent-color-rgb), 0.2); color: #333;">
                    اللون الشفاف<br>rgba(177, 156, 217, 0.2)
                </div>
            </div>

            <div class="form-preview">
                <div class="rid-cod-form-modern">
                    <div id="rid-cod-checkout">
                        <div class="rid-cod-title">
                            <h3>نموذج عصري - عينة</h3>
                        </div>
                        <div class="rid-cod-customer-info">
                            <div class="rid-cod-field-group">
                                <input type="text" placeholder="رقم الهاتف" value="0123456789">
                            </div>
                            <div class="rid-cod-field-group">
                                <input type="text" placeholder="الاسم الكامل" value="أحمد محمد">
                            </div>
                        </div>

                        <!-- Variations -->
                        <div class="rid-cod-variations">
                            <div class="variation-row">
                                <div class="variation-label">
                                    <span class="attribute-label">اللون:</span>
                                </div>
                                <div class="variation-boxes" data-attribute="color">
                                    <div class="variation-option color-option selected" data-value="red" style="background: #ff0000;"></div>
                                    <div class="variation-option color-option" data-value="green" style="background: #00ff00;"></div>
                                    <div class="variation-option color-option" data-value="blue" style="background: #0000ff;"></div>
                                </div>
                            </div>
                            <div class="variation-row">
                                <div class="variation-label">
                                    <span class="attribute-label">الحجم:</span>
                                </div>
                                <div class="variation-boxes" data-attribute="size">
                                    <div class="variation-option" data-value="S">S</div>
                                    <div class="variation-option selected" data-value="M">M</div>
                                    <div class="variation-option" data-value="L">L</div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions Row -->
                        <div class="rid-cod-actions-row">
                            <div class="rid-cod-quantity-selector">
                                <button type="button">-</button>
                                <input type="number" value="1" min="1">
                                <button type="button">+</button>
                            </div>
                            <button id="rid-cod-submit-btn" type="submit">طلب الآن</button>
                        </div>

                        <!-- Order Summary -->
                        <div id="rid-cod-summary-wrapper">
                            <div id="rid-cod-summary-header">
                                <h4>ملخص الطلب</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Section 3: System Summary -->
        <div class="test-section">
            <h2 class="test-title">3. ملخص نظام إدارة الألوان</h2>
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-right: 4px solid #28a745;">
                <h4 style="color: #155724; margin-top: 0;">✅ النظام المطبق:</h4>
                <ul style="color: #155724;">
                    <li><strong>النموذج الكلاسيكي:</strong> يستخدم #6a3de8 (البنفسجي الداكن) كلون افتراضي</li>
                    <li><strong>النموذج العصري:</strong> يستخدم #B19CD9 (البنفسجي الفاتح) كلون افتراضي</li>
                    <li><strong>التحديث التلقائي:</strong> عند اختيار أي تصميم، تتحدث جميع حقول الألوان فوراً</li>
                    <li><strong>علامة الاختيار:</strong> تظهر ✓ على التصميم المحدد</li>
                    <li><strong>إمكانية التخصيص:</strong> جميع الألوان قابلة للتعديل بعد اختيار التصميم</li>
                    <li><strong>الحفظ:</strong> عند النقر على "حفظ الإعدادات"، تحفظ جميع الألوان المخصصة</li>
                    <li><strong>متغيرات CSS:</strong> النموذج العصري يستخدم متغيرات CSS قابلة للتخصيص</li>
                </ul>
            </div>
            
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-right: 4px solid #ffc107; margin-top: 15px;">
                <h4 style="color: #856404; margin-top: 0;">📋 كيفية الاستخدام:</h4>
                <ol style="color: #856404;">
                    <li>اذهب إلى <strong>لوحة التحكم > WooCommerce > إعدادات الدفع عند الاستلام</strong></li>
                    <li>انقر على تبويبة <strong>"المظهر"</strong></li>
                    <li>اختر التصميم المطلوب (كلاسيكي أو عصري)</li>
                    <li>ستظهر الألوان الافتراضية للتصميم المحدد في حقول الألوان</li>
                    <li>يمكنك تعديل أي لون حسب رغبتك</li>
                    <li>انقر على <strong>"حفظ الإعدادات"</strong> لتطبيق التغييرات</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Test color switching
        function testColorSwitch(style) {
            const root = document.documentElement;
            if (style === 'classic') {
                root.style.setProperty('--rid-cod-accent-color', '#6a3de8');
                root.style.setProperty('--rid-cod-button-bg-color', '#6a3de8');
                root.style.setProperty('--rid-cod-accent-color-rgb', '106, 61, 232');
            } else if (style === 'modern') {
                root.style.setProperty('--rid-cod-accent-color', '#B19CD9');
                root.style.setProperty('--rid-cod-button-bg-color', '#B19CD9');
                root.style.setProperty('--rid-cod-accent-color-rgb', '177, 156, 217');
            }
        }

        // Add test buttons
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            const testButtons = document.createElement('div');
            testButtons.innerHTML = `
                <div style="text-align: center; margin: 20px 0;">
                    <button onclick="testColorSwitch('classic')" style="background: #6a3de8; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer;">
                        تجربة الألوان الكلاسيكية
                    </button>
                    <button onclick="testColorSwitch('modern')" style="background: #B19CD9; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer;">
                        تجربة الألوان العصرية
                    </button>
                </div>
            `;
            container.appendChild(testButtons);
        });
    </script>
</body>
</html>
