<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحديثات - النماذج المحدثة</title>
    <link rel="stylesheet" href="assets/css/rid-cod.css">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 40px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #6a3de8;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .color-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .color-control {
            display: inline-block;
            margin: 5px 10px;
        }
        .color-control label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        .color-control input[type="color"] {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .form-preview {
            margin-top: 20px;
        }
        /* Override CSS variables for testing */
        :root {
            --rid-cod-primary-bg-color: #ffffff;
            --rid-cod-button-bg-color: #B19CD9;
            --rid-cod-button-text-color: #ffffff;
            --rid-cod-accent-color: #B19CD9;
            --rid-cod-accent-color-rgb: 177, 156, 217;
            --rid-cod-border-color: #e0e0e0;
            --rid-cod-text-color: #333333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">اختبار التحديثات الجديدة</h1>
        
        <!-- Test Section 1: Color Customization -->
        <div class="test-section">
            <h2 class="test-title">1. اختبار تخصيص الألوان للنموذج العصري</h2>
            <div class="color-controls">
                <div class="color-control">
                    <label>اللون الأساسي:</label>
                    <input type="color" id="accent-color" value="#B19CD9">
                </div>
                <div class="color-control">
                    <label>لون الأزرار:</label>
                    <input type="color" id="button-color" value="#B19CD9">
                </div>
                <div class="color-control">
                    <label>لون النص:</label>
                    <input type="color" id="text-color" value="#333333">
                </div>
                <div class="color-control">
                    <label>لون الخلفية:</label>
                    <input type="color" id="bg-color" value="#ffffff">
                </div>
                <button onclick="resetColors()" style="margin-right: 20px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">إعادة تعيين</button>
            </div>
            
            <div class="form-preview">
                <div class="rid-cod-form-modern">
                    <div id="rid-cod-checkout">
                        <div class="rid-cod-title">
                            <h3>للطلب أدخل معلوماتك في الخانات أسفله ثم إضغط على زر طلب الآن 👇</h3>
                        </div>
                        
                        <div class="rid-cod-customer-info">
                            <div class="rid-cod-field-group">
                                <input type="text" placeholder="رقم الهاتف" value="0123456789">
                            </div>
                            <div class="rid-cod-field-group">
                                <input type="text" placeholder="الاسم الكامل" value="أحمد محمد">
                            </div>
                            <div class="rid-cod-field-group">
                                <select>
                                    <option>اختر المدينة</option>
                                    <option selected>الجزائر</option>
                                    <option>وهران</option>
                                </select>
                            </div>
                            <div class="rid-cod-field-group">
                                <select>
                                    <option>اختر البلدية</option>
                                    <option selected>وسط المدينة</option>
                                </select>
                            </div>
                        </div>

                        <!-- Delivery Type -->
                        <div class="rid-cod-delivery-type">
                            <div class="delivery-label">نوع التوصيل:</div>
                            <div class="delivery-options">
                                <label class="delivery-option">
                                    <input type="radio" name="delivery_type" value="home" checked>
                                    <span class="radio-custom"></span>
                                    <span class="delivery-text">توصيل للمنزل</span>
                                </label>
                                <label class="delivery-option">
                                    <input type="radio" name="delivery_type" value="office">
                                    <span class="radio-custom"></span>
                                    <span class="delivery-text">توصيل للمكتب</span>
                                </label>
                            </div>
                        </div>

                        <!-- Variations -->
                        <div class="rid-cod-variations">
                            <div class="variation-row">
                                <div class="variation-label">
                                    <span class="attribute-label">اللون:</span>
                                </div>
                                <div class="variation-boxes" data-attribute="color">
                                    <div class="variation-option color-option selected" data-value="red" style="background: #ff0000;"></div>
                                    <div class="variation-option color-option" data-value="green" style="background: #00ff00;"></div>
                                    <div class="variation-option color-option" data-value="blue" style="background: #0000ff;"></div>
                                    <div class="variation-option color-option" data-value="black" style="background: #000000;"></div>
                                </div>
                            </div>
                            <div class="variation-row">
                                <div class="variation-label">
                                    <span class="attribute-label">الحجم:</span>
                                </div>
                                <div class="variation-boxes" data-attribute="size">
                                    <div class="variation-option" data-value="S">S</div>
                                    <div class="variation-option selected" data-value="M">M</div>
                                    <div class="variation-option" data-value="L">L</div>
                                    <div class="variation-option" data-value="XL">XL</div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions Row -->
                        <div class="rid-cod-actions-row">
                            <div class="rid-cod-quantity-selector">
                                <button type="button">-</button>
                                <input type="number" value="1" min="1">
                                <button type="button">+</button>
                            </div>
                            <button id="rid-cod-submit-btn" type="submit">طلب الآن</button>
                        </div>

                        <!-- Order Summary -->
                        <div id="rid-cod-summary-wrapper">
                            <div id="rid-cod-summary-header">
                                <h4>ملخص الطلب</h4>
                            </div>
                            <div id="rid-cod-summary-content">
                                <table>
                                    <tr><td>المنتج:</td><td>قميص رياضي</td></tr>
                                    <tr><td>اللون:</td><td>أحمر</td></tr>
                                    <tr><td>الحجم:</td><td>M</td></tr>
                                    <tr><td>الكمية:</td><td>1</td></tr>
                                    <tr><td>نوع التوصيل:</td><td>توصيل للمنزل</td></tr>
                                    <tr><td><strong>المجموع:</strong></td><td><strong>2500 دج</strong></td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Section 2: Form Style Selection -->
        <div class="test-section">
            <h2 class="test-title">2. اختبار اختيار التصاميم (مع علامة الاختيار)</h2>
            <p><strong>ملاحظة:</strong> تم حذف النموذج الأحمر بنجاح. الآن يظهر فقط النموذج الكلاسيكي والعصري مع علامة اختيار على المحدد.</p>
            
            <div class="rid-cod-form-style-selector">
                <div class="form-style-options">
                    <label class="form-style-option">
                        <input type="radio" name="form_style_test" value="classic" />
                        <div class="form-style-preview">
                            <div class="preview-container classic-preview">
                                <div class="preview-title">النموذج الكلاسيكي</div>
                                <div class="preview-form">
                                    <div class="preview-field"></div>
                                    <div class="preview-field"></div>
                                    <div class="preview-button"></div>
                                </div>
                            </div>
                        </div>
                        <span class="style-name">النموذج الكلاسيكي</span>
                    </label>

                    <label class="form-style-option selected">
                        <input type="radio" name="form_style_test" value="modern" checked />
                        <div class="form-style-preview">
                            <div class="preview-container modern-preview">
                                <div class="preview-title">النموذج العصري</div>
                                <div class="preview-form">
                                    <div class="preview-field-row">
                                        <div class="preview-field"></div>
                                        <div class="preview-field"></div>
                                    </div>
                                    <div class="preview-field-row">
                                        <div class="preview-select"></div>
                                        <div class="preview-select"></div>
                                    </div>
                                    <div class="preview-variations">
                                        <div class="preview-color-options">
                                            <div class="preview-color red"></div>
                                            <div class="preview-color green"></div>
                                            <div class="preview-color black"></div>
                                        </div>
                                    </div>
                                    <div class="preview-button"></div>
                                </div>
                            </div>
                        </div>
                        <span class="style-name">النموذج العصري</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Test Section 3: Summary -->
        <div class="test-section">
            <h2 class="test-title">3. ملخص التحديثات</h2>
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-right: 4px solid #28a745;">
                <h4 style="color: #155724; margin-top: 0;">✅ تم تنفيذ جميع المطالب بنجاح:</h4>
                <ul style="color: #155724;">
                    <li><strong>حذف النموذج الأحمر:</strong> تم إزالة النموذج الأحمر بالكامل من خيارات الاختيار ومن ملف CSS</li>
                    <li><strong>علامة الاختيار:</strong> تظهر الآن علامة ✓ على التصميم المحدد عند النقر عليه</li>
                    <li><strong>النموذج العصري قابل للتخصيص:</strong> يمكن الآن تغيير ألوان النموذج العصري من إعدادات لوحة التحكم</li>
                    <li><strong>اللون الافتراضي الجديد:</strong> اللون الافتراضي للنموذج العصري أصبح #B19CD9 (بنفسجي فاتح)</li>
                    <li><strong>التفاعل المحسن:</strong> يمكن النقر على أي مكان في التصميم لاختياره وإظهار علامة الاختيار</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Color customization functionality
        function updateCSSVariable(property, value) {
            document.documentElement.style.setProperty(property, value);
        }

        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? 
                parseInt(result[1], 16) + ', ' + parseInt(result[2], 16) + ', ' + parseInt(result[3], 16) :
                '106, 61, 232';
        }

        document.getElementById('accent-color').addEventListener('change', function(e) {
            updateCSSVariable('--rid-cod-accent-color', e.target.value);
            updateCSSVariable('--rid-cod-accent-color-rgb', hexToRgb(e.target.value));
        });

        document.getElementById('button-color').addEventListener('change', function(e) {
            updateCSSVariable('--rid-cod-button-bg-color', e.target.value);
        });

        document.getElementById('text-color').addEventListener('change', function(e) {
            updateCSSVariable('--rid-cod-text-color', e.target.value);
        });

        document.getElementById('bg-color').addEventListener('change', function(e) {
            updateCSSVariable('--rid-cod-primary-bg-color', e.target.value);
        });

        function resetColors() {
            updateCSSVariable('--rid-cod-accent-color', '#B19CD9');
            updateCSSVariable('--rid-cod-accent-color-rgb', '177, 156, 217');
            updateCSSVariable('--rid-cod-button-bg-color', '#B19CD9');
            updateCSSVariable('--rid-cod-text-color', '#333333');
            updateCSSVariable('--rid-cod-primary-bg-color', '#ffffff');

            document.getElementById('accent-color').value = '#B19CD9';
            document.getElementById('button-color').value = '#B19CD9';
            document.getElementById('text-color').value = '#333333';
            document.getElementById('bg-color').value = '#ffffff';
        }

        // Form style selection - handle clicks on the entire option
        document.querySelectorAll('.form-style-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                document.querySelectorAll('.form-style-option').forEach(opt => {
                    opt.classList.remove('selected');
                });

                // Add selected class to clicked option
                this.classList.add('selected');

                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            });
        });

        // Also handle radio button changes directly
        document.querySelectorAll('input[name="form_style_test"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.querySelectorAll('.form-style-option').forEach(option => {
                    option.classList.remove('selected');
                });
                this.closest('.form-style-option').classList.add('selected');
            });
        });
    </script>
</body>
</html>
