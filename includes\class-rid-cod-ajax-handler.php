<?php
/**
 * AJAX Handler for License Management
 * Updated to use the new API system from /api/ endpoints
 * Handles license activation and deactivation via AJAX calls
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class RID_COD_Ajax_Handler {

    public function __construct() {
        // Hook the activation function to the AJAX action
        add_action('wp_ajax_ridcode_activate_license', array($this, 'handle_license_activation'));
        // Add hook for deactivation
        add_action('wp_ajax_ridcode_deactivate_license', array($this, 'handle_license_deactivation'));
    }    public function handle_license_activation() {
        // Verify nonce
        check_ajax_referer('ridcode_license_nonce', 'nonce');

        // Get the license key from the POST request
        $license_key = isset($_POST['license_key']) ? sanitize_text_field($_POST['license_key']) : '';

        if (empty($license_key)) {
            wp_send_json_error(array('message' => __('Please enter a license key.', 'rid-cod')));
            return;
        }

        // Use the updated API system
        $api_url = rid_cod_get_api_base_url() . 'license-verify.php';
        $domain = rid_cod_get_current_domain();

        $response = wp_remote_post($api_url, array(
            'timeout' => 45, // Set timeout
            'body'    => array(
                'license_key' => $license_key,
                'domain'     => $domain,
                'action'     => 'activate'
            ),
        ));

        // Check for WP errors during the request
        if (is_wp_error($response)) {
            wp_send_json_error(array('message' => __('Error connecting to license server:', 'rid-cod') . ' ' . $response->get_error_message()));
            return;
        }

        // Get the response body
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true); // Decode JSON response
        $response_code = wp_remote_retrieve_response_code($response);        // Check if the activation was successful
        if ($response_code === 200 && isset($data['status'])) {
            // Accept both 'active' and 'success' status
            if ($data['status'] === 'active' || $data['status'] === 'success') {
                // Save the license key
                update_option(RID_COD_LICENSE_OPTION_KEY, $license_key);
                // Set the license status to active
                update_option(RID_COD_LICENSE_STATUS_OPTION, 'active');
                // Clear license check transient
                delete_transient('rid_cod_license_check');
                
                // Use server message if available
                $success_message = isset($data['message']) ? $data['message'] : __('License activated successfully!', 'rid-cod');
                
                // Send success response
                wp_send_json_success(array(
                    'message' => $success_message,
                    'status'  => 'active'
                ));
            } else {
                // Activation failed - extract error message
                $error_message = isset($data['message']) ? $data['message'] : __('License verification failed.', 'rid-cod');
                if ($response_code !== 200) {
                    $error_message .= ' (HTTP ' . $response_code . ')';
                }
                
                // Send error response
                wp_send_json_error(array('message' => $error_message));
            }
        } else {
            // No valid status received
            $error_message = isset($data['message']) ? $data['message'] : __('Invalid response from server.', 'rid-cod');
            wp_send_json_error(array('message' => $error_message));
        }
    }    public function handle_license_deactivation() {
        // Verify nonce
        check_ajax_referer('ridcode_license_nonce', 'nonce');

        // Get the current license key
        $license_key = get_option(RID_COD_LICENSE_OPTION_KEY, '');

        if (empty($license_key)) {
            wp_send_json_error(array('message' => __('No active license found.', 'rid-cod')));
            return;
        }

        // Use the updated API system
        $api_url = rid_cod_get_api_base_url() . 'license-verify.php';
        $domain = rid_cod_get_current_domain();

        $response = wp_remote_post($api_url, array(
            'timeout' => 45, // Set timeout
            'body'    => array(
                'license_key' => $license_key,
                'domain'     => $domain,
                'action'     => 'deactivate'
            ),
        ));

        // Whether the server call was successful or not, deactivate locally
        update_option(RID_COD_LICENSE_STATUS_OPTION, 'inactive');
        delete_transient('rid_cod_license_check');

        // Check for WP errors during the request
        if (is_wp_error($response)) {
            wp_send_json_success(array(
                'message' => __('License deactivated locally. Server connection failed: ', 'rid-cod') . $response->get_error_message(),
                'status'  => 'inactive'
            ));
            return;
        }

        // Send success response
        wp_send_json_success(array(
            'message' => __('License deactivated successfully.', 'rid-cod'),
            'status'  => 'inactive'
        ));
    }
}
